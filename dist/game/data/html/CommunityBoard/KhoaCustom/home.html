<html>
<body><br>
<table width=755>
<tr>
<center><td align=center valign=top>
<table border=0 cellpadding=0 cellspacing=0 width=769 height=492 background="l2ui_ct1.Windows_DF_TooltipBG">
<tr>
<td valign="top" align="center">
<table width=755 height=468>
<tr>
<td width=755>
<table border=0 cellspacing=0 cellpadding=0 width=755>
<tr>
<td width=260>
<table width=260 height=180>
<tr>
<td valign="top" align="center">
<table border=0 width=260 height=240 cellspacing=4 cellpadding=3 background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td width=220 align=left valign=top>

<table width=260 height=25 bgcolor=000000>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Name</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="669900">%name%</font>
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=333333>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Class</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%class%</font>
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=000000>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Level</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%level%</font>
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=333333>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Clan</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%clan_name%</font>
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=000000>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Online Time</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%online_time%</font>
</td>
</tr>
</table>
<!-- <table width=260 height=25 bgcolor=000000>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Your IP</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%ip%</font>
</td>
</tr>
</table> -->
<table width=260 height=25 bgcolor=333333>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Premium Status</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%PREMIUM_STATUS%</font>
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=000000>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Premium Duration</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%PREMIUM_DURATION%</font>
</td>
</tr>
</table>

 <table width=260 height=25 bgcolor=333333>
  <tr>
   <td width=90 align=right valign=center>
    <font color="B59A75">Sharing Status</font>
   </td>
   <td width=4 align=center valign=center>
    :
   </td>
   <td width=90 align=left valign=center>
    <font color="FFFFFF">%sharing_duration_info%</font>
   </td>
  </tr>
 </table>

<table width=260 height=25 bgcolor=000000>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Premium Name</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%Premium_Name%</font>
</td>
</tr>
</table>

<table width=260 height=25 bgcolor=333333>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">GvE Skill Points</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%gve_skill_points%</font>
</td>
</tr>
</table>

<!-- <table width=255 height=25 bgcolor=000000>
<tr>
<td width=40 align=right valign=center>
<font color="B59A75">VIP Point</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=40 align=right valign=center>
<font color="FFFFFF">%VIP_Point%</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=40 align=left valign=center>
<font color="B59A75">Next Level</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=40 align=left valign=center>
<font color="FFFFFF">%VIP_Point_Next_Level%</font>
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=333333>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">VIP Duration</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%VIP_Duration%</font>
</td>
</tr>
</table> -->



</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
<td width=456>
<table width=456 height=180>
<tr>
<td valign="top" align="center">
<table border=0 width=456 height=240 cellspacing=4 cellpadding=3 background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td width=220 align=left valign=top>
<table width=456 height=23 background="l2ui_ct1.Windows_DF_Drawer_Bg">
<tr>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
<td width=416 align=center valign=center>
<font name="CreditTextNormal" color="CCFF99">Welcome to L2GVE</font>
</td>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
</tr>
</table>
<table width=456 height=25 bgcolor=000000>
<tr>
<td width=456 align=left valign=center>
 <font>Hello <font name="CreditTextNormal" color=669900>%name%</font>, and welcome to our grand new project. We've worked on <br1>
to be one of the biggest and greatest in 2023.<br1>
Enjoy playing on our server and if you have any suggestion, visit our forum or discord.</font>
</td>
</tr>
</table>
<table width=456 height=53 background="l2ui_ct1.Windows_DF_Drawer_Bg">
<tr>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
<td width=416 align=center valign=center>
<font name="CreditTextNormal" color="CCFF99">Server Rates</font>
</td>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
</tr>
</table>
<table width=456 height=25 bgcolor=000000>
<tr>
<td width=456 align=center valign=center>
<table align=center width=456 border=0>
<tr>
<td width=50 align=right><font color=B59A75>XP</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%xp_rate%</font></td>
<td width=50 align=right><font color=B59A75>SP</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%sp_rate%</font></td>
<td width=50 align=right><font color=B59A75>Adena Chance</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%adena_chance%</font></td>
</tr>
<tr>
<td width=50 align=right><font color=B59A75>Drop Chance</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%drop_chance%</font></td>
<td width=50 align=right><font color=B59A75>Spoil Chance</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%spoil_chance%</font></td>
<td width=50 align=right><font color=B59A75>Raid Chance</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%raid_chance%</font></td>
</tr>
<tr>
<td width=50 align=right><font color=B59A75>Adena Amount</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%adena_amount%</font></td>
<td width=50 align=right><font color=B59A75>Drop Amount</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%drop_amount%</font></td>
<td width=50 align=right><font color=B59A75>Raid Amount</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%raid_amount%</font></td>
</tr>
<tr>
<td width=50 align=right><font color=B59A75>Spoil Amount</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%spoil_amount%</font></td>
<td width=50 align=right><font color=B59A75>Quest Drop</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%quest_drop%</font></td>
<td width=50 align=right><font color=B59A75>Quest Reward</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%quest_reward%</font></td>
</tr>
<tr>
<td width=50 align=right><font color=B59A75>Quest XP</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%quest_reward_xp%</font></td>
<td width=50 align=right><font color=B59A75>Quest SP</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%quest_reward_sp%</font></td>
<td width=50 align=right><font color=B59A75>Quest Adena</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%quest_reward_adena%</font></td>
</tr>
</table>
</td>
</tr>
</table>
<!-- 
<table width=456 height=23 background="l2ui_ct1.Windows_DF_Drawer_Bg">
<tr>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
<td width=416 align=center valign=center>
<font name="CreditTextNormal" color="CCFF99">Server info</font>
</td>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
</tr>
</table>
<table width=456 height=23 bgcolor=000000>
<tr>
<td align=center valign=center width=150>
<font color="B59A75" name="CreditTextNormal">Time: </font><font name="CreditTextNormal"> %time% </font>
</td>
<td align=center valign=center width=150>
<font color="B59A75" name="CreditTextNormal">Online Now: </font><font name="CreditTextNormal"> %online% </font>
</td>
<td align=center valign=center width=150>
<font color="B59A75" name="CreditTextNormal">Offtraders: </font><font name="CreditTextNormal"> %offtrade% </font>
</td>
</tr>
</table>
 -->
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<table border=0 cellspacing=0 cellpadding=0 width=456>
<tr>
<td height=18></td>
</tr>
<tr>
<td width=260>

<table width=260 height=92>
<tr>
<td valign="top" align="center">
<table border=0 width=260 height=92 cellspacing=4 cellpadding=3 background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td width=220 align=left valign=top>
<table width=260 height=23 background="l2ui_ct1.Windows_DF_Drawer_Bg">
<tr>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
<td width=228 align=center valign=center>
<font color="CCFF99">Social Information</font>
</td>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=000000>
<tr>
<td width=260 height=55 align=center valign=center>
<table width=260 height=25>
<tr>
<td width=53 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.etc_scroll_of_tiger_b">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="url https://www.facebook.com/l2gvefaction" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Facebook
</td>
<td width=53 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.etc_scroll_of_tiger_a">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="url https://www.facebook.com/l2gvefaction" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Telegram
</td>
<td width=53 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.etc_scroll_of_transformation_traikan_i00">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="url https://discord.gg/4RPTDWdZhb" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Discord
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>

</td>
<td width=456>

<table width=456 height=92>
<tr>
<td valign="top" align="center">
<table border=0 width=455 height=92 cellspacing=4 cellpadding=3 background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td width=220 align=left valign=top>
<table width=455 height=23 background="l2ui_ct1.Windows_DF_Drawer_Bg">
<tr>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
<td width=416 align=center valign=center>
<font color="CCFF99">Premium Services</font>
</td>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
</tr>
</table>
<table width=454 height=25 bgcolor=000000>
<tr>
<td width=454 height=55 align=center valign=center>
<table width=400 height=25>
<tr>
<td width=200 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="icon.bm_royal_gold_ticket_all">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbspremium" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Premium
</td>
<td width=200 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.ability_lock">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbschangepass" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Password
</td>
<td width=200 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill6019">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbssearchdropCalc" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Drop Search
</td>
<td width=200 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.etc_treasure_box_i04">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbsgiftcode" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Giftcode
</td>

<!--
<td width=200 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="icon.bm_royal_gold_ticket_all">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbspremium30day" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
30-Days<br1>
(150 Pi)
</td>
 -->

</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>

</td>
</tr>
</table>
<table border=0 cellspacing=0 cellpadding=0 width=736>
<tr>
<td height=18></td>
</tr>
<tr>
<td width=736>
<table width=736 height=60>
<tr>
<td valign="top" align="center">
<table border=0 width=745 height=98 cellspacing=4 cellpadding=3 background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td width=736 align=center valign=center>
<table width=740 height=23 background="l2ui_ct1.Windows_DF_Drawer_Bg">
<tr>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
<td width=704 align=center valign=center>
<font color="CCFF99">Navigation</font>
</td>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
</tr>
</table>
<table width=755 height=60 bgcolor=000000>
<tr>

<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill5243">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass -h _bbstop;info" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Server Info
</td> -->

<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.vam_sleep_1"> <!-- Icon.ability_lock -->
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbstop_settings_menu" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Menu
</td>

<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill5243"> <!-- Icon.ability_lock -->
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbsstat" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Stats
</td>

<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.ability_lock"> Icon.ability_lock
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbschangepass" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Password
</td> -->

<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.vam_sleep_1">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass -h _bbstop_settings_menu" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Menu
</td> -->

<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="icon.stat_up_scroll">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass -h _bbsstats_debuff" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Stats
</td> -->

<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="branchsys2.br_vitality_day_i00">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass -h _bbsbufferbypass menu" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Buffer
</td> -->
<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="branchsys2.br_vitality_day_i00">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbsrps:0" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Masterio
</td>
<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill1526">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass -h _bbstop;commands.html" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Commands
</td> -->
<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.assist_of_shilen">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass -h _cbDkp" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Clan DKP
</td>
<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.penalty_armor">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass -h _cbDiscord" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Discord Bot
</td>
<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill5762">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _cbTelegram" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Telegram Bot
</td>
<!--
<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill5739">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass -h _bbstop;donate" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
How to Donation
</td>
-->
<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill5739">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbsmoneystore" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Balance Shop
</td>

<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="icon.skill5074">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbsfactiongve" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Faction War
</td>

<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="icon.rare_skill_book01">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbsgveskill" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
GvE Skill
</td>

<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="icon.skill1526">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbsfactionleader" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Faction Leader
</td>

<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill6019">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbssearchdropCalc" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Drop Search
</td> -->



<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="icon.skill5074">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass faction_change_personal" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Change Personal Faction
</td>

<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="icon.skill5074">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass faction_change_clan" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Change Clan Faction
</td> -->



</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<!--
<table border=0 cellspacing=0 cellpadding=0 width=755>
<tr>
<td height=18></td>
</tr>
<tr>
<td width=755>
<table width=755 height=20>
<tr>
<td valign="top" align="center">
<table border=0 width=755 height=20 cellspacing=4 cellpadding=3 background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td width=755 align=left valign=top>
<table width=745 height=25 bgcolor=0e0d0d>
<tr>
<td width=736 align=center valign=center>
<font color="LEVEL">Server Last Restarted at:</font> %server_uptime%
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
-->
</td>
</tr>
</table>
</td>
</tr>
</table>
</td></center>
</tr>
</table>
</body></html>
