package handlers.admincommandhandlers;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.StringTokenizer;

import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.data.xml.NpcData;
import club.projectessence.gameserver.features.museum.MuseumStatueInstance;
import club.projectessence.gameserver.handler.IAdminCommandHandler;
import club.projectessence.gameserver.features.museum.MuseumManager;
import club.projectessence.gameserver.features.museum.MuseumPillar;
import club.projectessence.gameserver.features.museum.RefreshTime;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;

/**
 * Simple Museum Test Admin Handler
 * Commands for testing museum functionality
 */
public class AdminMuseumTest implements IAdminCommandHandler
{
    private static final String[] ADMIN_COMMANDS = {
        "admin_museum_test",
        "admin_museum_add_data",
        "admin_museum_show_data",
        "admin_museum_spawn_test_statue",
        "admin_museum_spawn_normal_npc",
        "admin_museum_spawn_statue_as_npc",
        "admin_museum_spawn_statue_clone",
        "admin_museum_refresh_test",
        "admin_museum_clear_test",
        "admin_museum_pillars_test",
        "admin_museum_clear_all_data",
        "admin_museum_reset_system",
        "admin_museum_check_activity",
        "admin_museum_cleanup",
        "admin_museum_cleanup_last_stats",
        "admin_museum_refresh_statues"
    };
    
    @Override
    public boolean useAdminCommand(String command, PlayerInstance activeChar)
    {
        StringTokenizer st = new StringTokenizer(command, " ");
        String actualCommand = st.nextToken();
        
        if ("admin_museum_test".equals(actualCommand))
        {
            showTestPage(activeChar);
        }
        else if ("admin_museum_add_data".equals(actualCommand))
        {
            if (st.hasMoreTokens())
            {
                String category = st.nextToken();
                long value = st.hasMoreTokens() ? Long.parseLong(st.nextToken()) : 1;
                addTestData(activeChar, category, value);
            }
            else
            {
                activeChar.sendMessage("Usage: //museum_add_data <category> [value]");
                activeChar.sendMessage("Categories: boss_kills, pvp_kills, gve_points, faction_points");
            }
        }
        else if ("admin_museum_show_data".equals(actualCommand))
        {
            showPlayerData(activeChar);
        }
        else if ("admin_museum_spawn_test_statue".equals(actualCommand))
        {
            spawnTestStatue(activeChar);
        }
        else if ("admin_museum_spawn_normal_npc".equals(actualCommand))
        {
            spawnNormalNpc(activeChar);
        }
        else if ("admin_museum_spawn_statue_as_npc".equals(actualCommand))
        {
            spawnStatueAsNpc(activeChar);
        }
        else if ("admin_museum_spawn_statue_clone".equals(actualCommand))
        {
            //spawnStatueClone(activeChar);
        }
        else if ("admin_museum_refresh_test".equals(actualCommand))
        {
            refreshTestRankings(activeChar);
        }
        else if ("admin_museum_clear_test".equals(actualCommand))
        {
            clearTestData(activeChar);
        }
        else if ("admin_museum_pillars_test".equals(actualCommand))
        {
            testPillars(activeChar);
        }
        else if ("admin_museum_clear_all_data".equals(actualCommand))
        {
            clearAllMuseumData(activeChar);
        }
        else if ("admin_museum_reset_system".equals(actualCommand))
        {
            resetMuseumSystem(activeChar);
        }
        else if ("admin_museum_check_activity".equals(actualCommand))
        {
            if (st.hasMoreTokens())
            {
                String playerName = st.nextToken();
                String period = st.hasMoreTokens() ? st.nextToken() : "Weekly";
                checkPlayerActivity(activeChar, playerName, period);
            }
            else
            {
                activeChar.sendMessage("Usage: //admin_museum_check_activity <playerName> [Daily|Weekly|Monthly]");
            }
        }
        else if ("admin_museum_cleanup".equals(actualCommand))
        {
            cleanupInactiveRecords(activeChar);
        }
        else if ("admin_museum_cleanup_last_stats".equals(actualCommand))
        {
            if (st.hasMoreTokens())
            {
                String period = st.nextToken();
                cleanupInactiveLastStats(activeChar, period);
            }
            else
            {
                activeChar.sendMessage("Usage: //admin_museum_cleanup_last_stats [Daily|Weekly|Monthly]");
            }
        }
        else if ("admin_museum_refresh_statues".equals(actualCommand))
        {
            refreshAllStatues(activeChar);
        }

        return true;
    }
    
    private void showTestPage(PlayerInstance activeChar)
    {
        StringBuilder html = new StringBuilder();
        html.append("<html><body>");
        html.append("<center><font color=\"LEVEL\">Museum Test Panel</font></center><br>");
        html.append("<table width=\"280\">");
        
        // Quick Test Commands
        html.append("<tr><td><font color=\"FFCC00\">Quick Tests:</font></td></tr>");
        html.append("<tr><td><button value=\"Add Boss Kill\" action=\"bypass -h admin_museum_add_data boss_kills 1\" width=140 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("<tr><td><button value=\"Add PvP Kill\" action=\"bypass -h admin_museum_add_data pvp_kills 1\" width=140 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("<tr><td><button value=\"Add 100 GVE Points\" action=\"bypass -h admin_museum_add_data gve_points 100\" width=140 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("<tr><td height=\"10\"></td></tr>");
        
        // Management Commands
        html.append("<tr><td><font color=\"FFCC00\">Management:</font></td></tr>");
        html.append("<tr><td><button value=\"Show My Data\" action=\"bypass -h admin_museum_show_data\" width=140 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("<tr><td><button value=\"Spawn Test Statue\" action=\"bypass -h admin_museum_spawn_test_statue\" width=140 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("<tr><td><button value=\"Spawn Normal NPC\" action=\"bypass -h admin_museum_spawn_normal_npc\" width=140 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("<tr><td><button value=\"Spawn Statue as NPC\" action=\"bypass -h admin_museum_spawn_statue_as_npc\" width=140 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("<tr><td><button value=\"Refresh Rankings\" action=\"bypass -h admin_museum_refresh_test\" width=140 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("<tr><td><button value=\"Clear My Data\" action=\"bypass -h admin_museum_clear_test\" width=140 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("<tr><td height=\"10\"></td></tr>");

        // Debug Commands
        html.append("<tr><td><font color=\"FFCC00\">Debug & Fix:</font></td></tr>");
        html.append("<tr><td><button value=\"Check My Activity\" action=\"bypass -h admin_museum_check_activity " + activeChar.getName() + " Weekly\" width=140 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("<tr><td><button value=\"Cleanup Inactive\" action=\"bypass -h admin_museum_cleanup\" width=140 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("<tr><td><button value=\"Cleanup Weekly Stats\" action=\"bypass -h admin_museum_cleanup_last_stats Weekly\" width=140 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        html.append("<tr><td><button value=\"Refresh All Statues\" action=\"bypass -h admin_museum_refresh_statues\" width=140 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
        
        html.append("</table>");
        html.append("</body></html>");
        
        NpcHtmlMessage msg = new NpcHtmlMessage();
        msg.setHtml(html.toString());
        activeChar.sendPacket(msg);
    }
    
    private void addTestData(PlayerInstance player, String category, long value)
    {
        try (Connection con = DatabaseFactory.getConnection())
        {
            // Insert or update museum_statistics
            String sql = "INSERT INTO museum_statistics (objectId, name, category, total_count, monthly_count, weekly_count, daily_count, hasReward) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, 0) " +
                        "ON DUPLICATE KEY UPDATE " +
                        "total_count = total_count + ?, " +
                        "monthly_count = monthly_count + ?, " +
                        "weekly_count = weekly_count + ?, " +
                        "daily_count = daily_count + ?";
            
            try (PreparedStatement ps = con.prepareStatement(sql))
            {
                ps.setInt(1, player.getObjectId());
                ps.setString(2, player.getName());
                ps.setString(3, category);
                ps.setLong(4, value); // total_count
                ps.setLong(5, value); // monthly_count
                ps.setLong(6, value); // weekly_count
                ps.setLong(7, value); // daily_count
                ps.setLong(8, value); // total_count update
                ps.setLong(9, value); // monthly_count update
                ps.setLong(10, value); // weekly_count update
                ps.setLong(11, value); // daily_count update
                
                ps.executeUpdate();
                player.sendMessage("Added " + value + " to category: " + category);
                player.sendMessage("Database updated successfully!");
            }
        }
        catch (Exception e)
        {
            player.sendMessage("Error adding data: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void showPlayerData(PlayerInstance player)
    {
        try (Connection con = DatabaseFactory.getConnection();
             PreparedStatement ps = con.prepareStatement(
                 "SELECT category, total_count, monthly_count, weekly_count, daily_count FROM museum_statistics WHERE objectId = ?"))
        {
            ps.setInt(1, player.getObjectId());
            
            StringBuilder message = new StringBuilder();
            message.append("=== Museum Data for ").append(player.getName()).append(" ===\n");
            
            try (ResultSet rs = ps.executeQuery())
            {
                boolean hasData = false;
                while (rs.next())
                {
                    hasData = true;
                    String category = rs.getString("category");
                    long total = rs.getLong("total_count");
                    long monthly = rs.getLong("monthly_count");
                    long weekly = rs.getLong("weekly_count");
                    long daily = rs.getLong("daily_count");
                    
                    message.append(category).append(": Total=").append(total)
                           .append(", Monthly=").append(monthly)
                           .append(", Weekly=").append(weekly)
                           .append(", Daily=").append(daily).append("\n");
                }
                
                if (!hasData)
                {
                    message.append("No museum data found for this player.");
                }
            }
            
            player.sendMessage(message.toString());
        }
        catch (Exception e)
        {
            player.sendMessage("Error showing data: " + e.getMessage());
        }
    }
    
    private void spawnTestStatue(PlayerInstance player)
    {
        try
        {
            // Check if NPC template exists
            NpcTemplate template = NpcData.getInstance().getTemplate(30001); // Lector
            if (template == null)
            {
                template = NpcData.getInstance().getTemplate(31); // Fallback to Guard
                if (template == null)
                {
                    player.sendMessage("Error: No valid NPC template found for statue!");
                    return;
                }
            }

            // Spawn a test statue near the player
            Location loc = new Location(player.getX() + 100, player.getY(), player.getZ(), player.getHeading());

            MuseumStatueInstance statue = new MuseumStatueInstance(
                template,
                player.getObjectId(),
                1 // Test type
            );

            statue.setXYZ(loc);
            statue.setHeading(loc.getHeading());

            // Make sure statue is visible and targetable
            statue.setTargetable(true);
           // statue.setShowName(true);

            statue.spawnMe();

            // Force broadcast to all nearby players
            statue.broadcastInfo();

            // Don't send info directly to avoid multiple packets
            // statue.sendInfo(player);



            player.sendMessage("Museum statue spawned successfully at: " + loc.getX() + "," + loc.getY() + "," + loc.getZ());




            player.broadcastUserInfo();
        }
        catch (Exception e)
        {
            player.sendMessage("Error spawning statue: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void spawnNormalNpc(PlayerInstance player)
    {
        try
        {
            // Check if NPC template exists
            NpcTemplate template = NpcData.getInstance().getTemplate(30001); // Lector
            if (template == null)
            {
                template = NpcData.getInstance().getTemplate(31); // Fallback to Guard
                if (template == null)
                {
                    player.sendMessage("Error: No valid NPC template found!");
                    return;
                }
            }

            // Spawn a normal NPC near the player for comparison
            Location loc = new Location(player.getX() + 150, player.getY(), player.getZ(), player.getHeading());

            // Import Npc class
            club.projectessence.gameserver.model.actor.Npc npc = new club.projectessence.gameserver.model.actor.Npc(template);

            npc.setXYZ(loc);
            npc.setHeading(loc.getHeading());
            npc.setTitle("Normal NPC Test");
            npc.spawnMe();

            player.sendMessage("Normal NPC spawned at: " + loc.getX() + "," + loc.getY() + "," + loc.getZ());
            player.sendMessage("NPC template: " + template.getId() + " (" + template.getName() + ")");
        }
        catch (Exception e)
        {
            player.sendMessage("Error spawning normal NPC: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void spawnStatueAsNpc(PlayerInstance player)
    {
        try
        {
            // Check if NPC template exists
            NpcTemplate template = NpcData.getInstance().getTemplate(30001); // Lector
            if (template == null)
            {
                template = NpcData.getInstance().getTemplate(31); // Fallback to Guard
                if (template == null)
                {
                    player.sendMessage("Error: No valid NPC template found for statue!");
                    return;
                }
            }

            // Spawn a test statue near the player but override sendInfo to use normal NpcInfo
            Location loc = new Location(player.getX() + 200, player.getY(), player.getZ(), player.getHeading());

            MuseumStatueInstance statue = new MuseumStatueInstance(
                template,
                player.getObjectId(),
                1 // Test type
            ) {
                @Override
                public void sendInfo(PlayerInstance player)
                {
                    if (isVisibleFor(player))
                    {
                        // Use normal NPC info instead of custom packet
                        player.sendPacket(new club.projectessence.gameserver.network.serverpackets.NpcInfo(this));
                    }
                }
            };

            statue.setXYZ(loc);
            statue.setHeading(loc.getHeading());
            statue.setTitle("Statue as NPC");

            // Make sure statue is visible and targetable
            statue.setTargetable(true);
            //statue.setShowName(true);

            statue.spawnMe();
            statue.broadcastInfo();

            player.sendMessage("Museum statue clone spawned at: " + loc.getX() + "," + loc.getY() + "," + loc.getZ());


        }
        catch (Exception e)
        {
            player.sendMessage("Error spawning statue as NPC: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void refreshTestRankings(PlayerInstance player)
    {
        try
        {
            // Copy current stats to last_statistics for testing
            try (Connection con = DatabaseFactory.getConnection())
            {
                String sql = "INSERT INTO museum_last_statistics (objectId, name, category, count, timer) " +
                           "SELECT objectId, name, category, total_count, 'total' FROM museum_statistics " +
                           "WHERE total_count > 0 " +
                           "ON DUPLICATE KEY UPDATE count = VALUES(count)";
                
                try (PreparedStatement ps = con.prepareStatement(sql))
                {
                    int updated = ps.executeUpdate();
                    player.sendMessage("Refreshed rankings: " + updated + " records updated");
                }
            }
            
            // Trigger MuseumManager refresh if available
            if (MuseumManager.getInstance() != null)
            {
                MuseumManager.getInstance().reloadConfigs();
                player.sendMessage("MuseumManager refreshed");
            }
        }
        catch (Exception e)
        {
            player.sendMessage("Error refreshing rankings: " + e.getMessage());
        }
    }
    
    private void clearTestData(PlayerInstance player)
    {
        try (Connection con = DatabaseFactory.getConnection())
        {
            // Clear from both tables
            try (PreparedStatement ps1 = con.prepareStatement("DELETE FROM museum_statistics WHERE objectId = ?");
                 PreparedStatement ps2 = con.prepareStatement("DELETE FROM museum_last_statistics WHERE objectId = ?"))
            {
                ps1.setInt(1, player.getObjectId());
                ps2.setInt(1, player.getObjectId());
                
                int deleted1 = ps1.executeUpdate();
                int deleted2 = ps2.executeUpdate();
                
                player.sendMessage("Cleared " + deleted1 + " records from museum_statistics");
                player.sendMessage("Cleared " + deleted2 + " records from museum_last_statistics");
            }
        }
        catch (Exception e)
        {
            player.sendMessage("Error clearing data: " + e.getMessage());
        }
    }

    private void testPillars(PlayerInstance activeChar)
    {
        try
        {
            activeChar.sendMessage("=== Museum Pillar Test ===");

            // Add some test data for current player
            MuseumManager.getInstance().addFactionPvpKill(activeChar);
            MuseumManager.getInstance().addFactionWarPoints(activeChar, 100);
            MuseumManager.getInstance().addPvpVictory(activeChar);
            MuseumManager.getInstance().addDailyGvePoints(activeChar, 50);
            MuseumManager.getInstance().addGveSkillsLearned(activeChar);
            MuseumManager.getInstance().addChaoticRaidsTotal(activeChar);
            MuseumManager.getInstance().addLeadershipActivity(activeChar);

            activeChar.sendMessage("Added test data for " + activeChar.getName());

            // Force update pillars
            MuseumManager.getInstance().updatePillarLeaders();
            activeChar.sendMessage("Updated pillar leaders");

            // Show pillar scores
            for (String pillarName : MuseumManager.getInstance().getAllPillars().keySet())
            {
                double score = MuseumManager.getInstance().calculatePillarScore(activeChar, pillarName);
                activeChar.sendMessage(pillarName + ": " + String.format("%.2f", score));
            }

            activeChar.sendMessage("Use //museum_pillars to see full pillar info");
        }
        catch (Exception e)
        {
            activeChar.sendMessage("Error testing pillars: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void clearAllMuseumData(PlayerInstance activeChar)
    {
        try
        {
            activeChar.sendMessage("=== Clearing All Museum Data ===");
            activeChar.sendMessage("WARNING: This will delete ALL museum statistics!");
            activeChar.sendMessage("Type //museum_reset_system to confirm and proceed.");
        }
        catch (Exception e)
        {
            activeChar.sendMessage("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void resetMuseumSystem(PlayerInstance activeChar)
    {
        try
        {
            activeChar.sendMessage("=== Resetting Museum System ===");

            // 1. Clear all pillar statues
            int clearedStatues = 0;
            for (MuseumPillar pillar : MuseumManager.getInstance().getAllPillars().values())
            {
                if (pillar.getSpawnedStatue() != null)
                {
                    pillar.getSpawnedStatue().deleteMe();
                    pillar.setSpawnedStatue(null);
                    clearedStatues++;
                }
            }
            activeChar.sendMessage("Cleared " + clearedStatues + " pillar statues");

            // 2. Clear database tables
            try (Connection con = DatabaseFactory.getConnection())
            {
                // Clear museum_statistics
                try (PreparedStatement statement = con.prepareStatement("DELETE FROM museum_statistics"))
                {
                    int deletedStats = statement.executeUpdate();
                    activeChar.sendMessage("Deleted " + deletedStats + " museum statistics records");
                }

                // Clear museum_last_statistics
                try (PreparedStatement statement = con.prepareStatement("DELETE FROM museum_last_statistics"))
                {
                    int deletedLastStats = statement.executeUpdate();
                    activeChar.sendMessage("Deleted " + deletedLastStats + " museum last statistics records");
                }
            }

            // 3. Clear in-memory data for all online players
            int clearedPlayers = 0;
            for (PlayerInstance player : World.getInstance().getPlayers())
            {
                if (player.getMuseumPlayer() != null)
                {
                    player.setMuseumPlayer(null);
                    clearedPlayers++;
                }
            }
            activeChar.sendMessage("Cleared museum data for " + clearedPlayers + " online players");

            // 4. Reset server max values
            MuseumManager.getInstance().updateServerMaxValues();
            activeChar.sendMessage("Reset server max values");

            // 5. Reset pillar leaders
            for (MuseumPillar pillar : MuseumManager.getInstance().getAllPillars().values())
            {
                pillar.setCurrentLeader(null);
                pillar.setCurrentLeaderScore(0.0);
            }
            activeChar.sendMessage("Reset all pillar leaders");

            activeChar.sendMessage("=== Museum System Reset Complete ===");
            activeChar.sendMessage("The system is now ready for live server use!");

        }
        catch (Exception e)
        {
            activeChar.sendMessage("Error resetting museum system: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Check player activity in specific period
     */
    private void checkPlayerActivity(PlayerInstance activeChar, String playerName, String period)
    {
        try
        {
            RefreshTime refreshTime;
            switch (period.toLowerCase())
            {
                case "daily":
                    refreshTime = RefreshTime.Daily;
                    break;
                case "weekly":
                    refreshTime = RefreshTime.Weekly;
                    break;
                case "monthly":
                    refreshTime = RefreshTime.Monthly;
                    break;
                default:
                    refreshTime = RefreshTime.Weekly;
                    break;
            }

            String report = MuseumManager.getInstance().checkPlayerActivity(playerName, refreshTime);

            // Send report to admin
            String[] lines = report.split("\n");
            for (String line : lines)
            {
                if (!line.trim().isEmpty())
                {
                    activeChar.sendMessage(line);
                }
            }
        }
        catch (Exception e)
        {
            activeChar.sendMessage("Error checking player activity: " + e.getMessage());
        }
    }

    /**
     * Clean up inactive records
     */
    private void cleanupInactiveRecords(PlayerInstance activeChar)
    {
        try
        {
            activeChar.sendMessage("Starting cleanup of inactive museum records...");
            MuseumManager.getInstance().cleanupInactiveRecords();
            activeChar.sendMessage("Cleanup completed successfully!");
        }
        catch (Exception e)
        {
            activeChar.sendMessage("Error during cleanup: " + e.getMessage());
        }
    }

    /**
     * Clean up inactive last statistics for specific period
     */
    private void cleanupInactiveLastStats(PlayerInstance activeChar, String period)
    {
        try
        {
            RefreshTime refreshTime;
            switch (period.toLowerCase())
            {
                case "daily":
                    refreshTime = RefreshTime.Daily;
                    break;
                case "weekly":
                    refreshTime = RefreshTime.Weekly;
                    break;
                case "monthly":
                    refreshTime = RefreshTime.Monthly;
                    break;
                default:
                    activeChar.sendMessage("Invalid period. Use: Daily, Weekly, or Monthly");
                    return;
            }

            activeChar.sendMessage("Cleaning up inactive " + period.toLowerCase() + " last statistics...");
            MuseumManager.getInstance().cleanupInactiveLastStatistics(refreshTime);
            activeChar.sendMessage("Cleanup completed for " + period.toLowerCase() + " period!");
        }
        catch (Exception e)
        {
            activeChar.sendMessage("Error during cleanup: " + e.getMessage());
        }
    }

    /**
     * Refresh all museum statues
     */
    private void refreshAllStatues(PlayerInstance activeChar)
    {
        try
        {
            activeChar.sendMessage("Refreshing all museum statues...");
            MuseumManager.getInstance().refreshAllStatues();
            activeChar.sendMessage("All statues refreshed successfully!");
        }
        catch (Exception e)
        {
            activeChar.sendMessage("Error refreshing statues: " + e.getMessage());
        }
    }

    @Override
    public String[] getAdminCommandList()
    {
        return ADMIN_COMMANDS;
    }
}
