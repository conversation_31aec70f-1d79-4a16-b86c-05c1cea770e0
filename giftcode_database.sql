-- Giftcode System Database Schema
-- T<PERSON><PERSON> bảng giftcodes với các biện pháp bảo mật

DROP TABLE IF EXISTS `giftcodes`;
CREATE TABLE `giftcodes` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `code` VARCHAR(6) NOT NULL UNIQUE,
  `type` TINYINT NOT NULL DEFAULT 1 COMMENT '1=Premium 7 days, 2=1000 Prime Points',
  `created_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by_gm` VARCHAR(45) DEFAULT NULL COMMENT 'GM account tạo code',
  `used_by_account` VARCHAR(45) DEFAULT NULL,
  `used_date` TIMESTAMP NULL DEFAULT NULL,
  `used_ip` VARCHAR(45) DEFAULT NULL COMMENT 'IP sử dụng code để track',
  `used_hwid` VARCHAR(255) DEFAULT NULL COMMENT 'HWID để track multi-account',
  `expires_date` TIMESTAMP NULL DEFAULT NULL COMMENT 'Ngày hết hạn code',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `usage_count` INT NOT NULL DEFAULT 0 COMMENT 'Số lần thử sử dụng (để track spam)',
  PRIMARY KEY (`id`),
  INDEX `idx_code` (`code`),
  INDEX `idx_used_account` (`used_by_account`),
  INDEX `idx_created_date` (`created_date`),
  INDEX `idx_expires_date` (`expires_date`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Bảng log giftcode để audit trail
DROP TABLE IF EXISTS `giftcode_logs`;
CREATE TABLE `giftcode_logs` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `account_name` VARCHAR(45) NOT NULL,
  `giftcode` VARCHAR(6) NOT NULL,
  `action` VARCHAR(20) NOT NULL COMMENT 'USED, FAILED, EXPIRED, INVALID',
  `ip_address` VARCHAR(45) DEFAULT NULL,
  `hwid` VARCHAR(255) DEFAULT NULL,
  `timestamp` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `details` TEXT DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_account` (`account_name`),
  INDEX `idx_timestamp` (`timestamp`),
  INDEX `idx_action` (`action`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Bảng rate limiting để chống spam
DROP TABLE IF EXISTS `giftcode_rate_limit`;
CREATE TABLE `giftcode_rate_limit` (
  `account_name` VARCHAR(45) NOT NULL,
  `ip_address` VARCHAR(45) NOT NULL,
  `attempts` INT NOT NULL DEFAULT 1,
  `last_attempt` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `blocked_until` TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (`account_name`, `ip_address`),
  INDEX `idx_last_attempt` (`last_attempt`),
  INDEX `idx_blocked_until` (`blocked_until`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
