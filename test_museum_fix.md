# Museum System Activity Fix - Test Plan

## Vấn đề đã sửa:
1. **Logic kiểm tra hoạt động tối thiểu**: Chỉ players có hoạt động > 0 trong chu kỳ hiện tại mới được đưa vào rankings
2. **Cleanup records không hoạt động**: Tự động xóa records của players không có hoạt động
3. **Validation khi lưu museum_last_statistics**: Chỉ lưu players thực sự hoạt động
4. **Admin commands để debug**: Thêm tools kiểm tra hoạt động và cleanup

## Các thay đổi đã thực hiện:

### 1. MuseumManager.java:
- **refreshTopsFromDatabase()**: Thêm validation hoạt động tối thiểu
- **cleanupInactiveRecords()**: Method mới để cleanup records không hoạt động  
- **checkPlayerActivity()**: Method debug để kiểm tra hoạt động player
- **UpdateStats.refreshTops()**: Gọi cleanup trước khi refresh rankings

### 2. AdminMuseumTest.java:
- **admin_museum_check_activity**: Command kiểm tra hoạt động player
- **admin_museum_cleanup**: Command cleanup records không hoạt động
- **UI buttons**: Thêm buttons trong test panel

## Cách test:

### Test 1: Kiểm tra character admin hiện tại
```
//admin_museum_check_activity [TenCharacterAdmin] Weekly
//admin_museum_check_activity [TenCharacterAdmin] Daily  
//admin_museum_check_activity [TenCharacterAdmin] Monthly
```

### Test 2: Cleanup records không hoạt động
```
//admin_museum_cleanup
```

### Test 3: Test với character có hoạt động thực
1. Tạo hoạt động mới cho character test:
```
//admin_museum_add_data faction_pvp_kills 5
//admin_museum_add_data daily_gve_points 50
```

2. Kiểm tra hoạt động:
```
//admin_museum_check_activity [TenCharacterTest] Daily
```

3. Refresh rankings:
```
//admin_museum_refresh_test
```

### Test 4: Kiểm tra museum rankings sau fix
1. Vào Community Board -> Museum
2. Kiểm tra các rankings daily/weekly/monthly
3. Xác nhận chỉ có players có hoạt động thực sự

## Kết quả mong đợi:

### Trước fix:
- Character admin xuất hiện trong rankings mặc dù không có hoạt động
- Tượng được tạo cho character không hoạt động

### Sau fix:
- Chỉ players có hoạt động > 0 trong chu kỳ hiện tại mới xuất hiện trong rankings
- Không tạo tượng cho players không hoạt động
- Database được cleanup định kỳ

## Lưu ý:
- Fix này áp dụng cho tất cả 7 loại museum categories
- Không ảnh hưởng đến Total rankings (vẫn hiển thị tất cả)
- Chỉ ảnh hưởng đến Daily/Weekly/Monthly rankings
- Cleanup chạy tự động mỗi khi reset chu kỳ
